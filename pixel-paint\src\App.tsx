import { useState, useEffect } from 'react'
import PixelCanvas from './components/PixelCanvas'
import Toolbar from './components/Toolbar'
import ColorPalette from './components/ColorPalette'

export type Tool = 'pencil' | 'eyedropper' | 'eraser'

function App() {
  const [canvasSize, setCanvasSize] = useState({ width: 16, height: 16 })
  const [currentTool, setCurrentTool] = useState<Tool>('pencil')
  const [currentColor, setCurrentColor] = useState('#000000')
  const [zoom, setZoom] = useState(20)
  const [customPalette, setCustomPalette] = useState<Record<string, string>>({})

  // Загрузка кастомной палитры
  useEffect(() => {
    fetch('/palette.json')
      .then(response => response.json())
      .then(data => setCustomPalette(data))
      .catch(error => console.error('Ошибка загрузки палитры:', error))
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-6 text-gray-800">
          Pixel Paint
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Панель инструментов */}
          <div className="lg:col-span-1">
            <Toolbar
              currentTool={currentTool}
              onToolChange={setCurrentTool}
              canvasSize={canvasSize}
              onCanvasSizeChange={setCanvasSize}
              zoom={zoom}
              onZoomChange={setZoom}
            />

            <div className="mt-6">
              <ColorPalette
                currentColor={currentColor}
                onColorChange={setCurrentColor}
                customPalette={customPalette}
              />
            </div>
          </div>

          {/* Холст */}
          <div className="lg:col-span-3 flex justify-center">
            <PixelCanvas
              width={canvasSize.width}
              height={canvasSize.height}
              currentTool={currentTool}
              currentColor={currentColor}
              zoom={zoom}
              onColorPick={setCurrentColor}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
