interface ColorPaletteProps {
  currentColor: string
  onColorChange: (color: string) => void
  customPalette: Record<string, string>
}

const ColorPalette = ({ currentColor, onColorChange, customPalette }: ColorPaletteProps) => {
  // Стандартная палитра
  const standardColors = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', 
    '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
    '#FFA500', '#800080', '#FFC0CB', '#A52A2A',
    '#808080', '#C0C0C0', '#800000', '#008000'
  ]

  const ColorButton = ({ color, name }: { color: string; name?: string }) => (
    <button
      onClick={() => onColorChange(color)}
      className={`w-8 h-8 rounded border-2 transition-all hover:scale-110 ${
        currentColor === color 
          ? 'border-gray-800 shadow-lg' 
          : 'border-gray-300 hover:border-gray-500'
      }`}
      style={{ backgroundColor: color }}
      title={name || color}
    />
  )

  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4 text-gray-800">Палитра цветов</h3>
      
      {/* Текущий цвет */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-sm text-gray-600">Текущий цвет:</span>
          <div 
            className="w-6 h-6 rounded border border-gray-300"
            style={{ backgroundColor: currentColor }}
          />
          <span className="text-xs text-gray-500">{currentColor}</span>
        </div>
        <input
          type="color"
          value={currentColor}
          onChange={(e) => onColorChange(e.target.value)}
          className="w-full h-8 rounded border border-gray-300 cursor-pointer"
        />
      </div>

      {/* Стандартная палитра */}
      <div className="mb-4">
        <h4 className="text-md font-medium mb-2 text-gray-700">Стандартные цвета</h4>
        <div className="grid grid-cols-4 gap-2">
          {standardColors.map(color => (
            <ColorButton key={color} color={color} />
          ))}
        </div>
      </div>

      {/* Кастомная палитра */}
      {Object.keys(customPalette).length > 0 && (
        <div>
          <h4 className="text-md font-medium mb-2 text-gray-700">Кастомная палитра</h4>
          <div className="space-y-1 max-h-48 overflow-y-auto">
            {Object.entries(customPalette).map(([color, name]) => (
              <div key={color} className="flex items-center space-x-2">
                <ColorButton color={color} name={name} />
                <span className="text-xs text-gray-600 flex-1">{name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default ColorPalette
