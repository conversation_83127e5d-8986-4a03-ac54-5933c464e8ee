import { useState, useRef, useEffect } from 'react'

interface PixelCanvasProps {
  width: number
  height: number
  currentTool: 'pencil' | 'eyedropper' | 'eraser'
  currentColor: string
  zoom: number
  onColorPick: (color: string) => void
}

interface Pixel {
  color: string
}

const PixelCanvas = ({ 
  width, 
  height, 
  currentTool, 
  currentColor, 
  zoom, 
  onColorPick 
}: PixelCanvasProps) => {
  const [pixels, setPixels] = useState<Pixel[][]>(() => 
    Array(height).fill(null).map(() => 
      Array(width).fill(null).map(() => ({ color: '#FFFFFF' }))
    )
  )
  const [isDrawing, setIsDrawing] = useState(false)
  const canvasRef = useRef<HTMLDivElement>(null)

  // Обновляем пиксели при изменении размера холста
  useEffect(() => {
    setPixels(prev => {
      const newPixels = Array(height).fill(null).map((_, y) =>
        Array(width).fill(null).map((_, x) => {
          if (prev[y] && prev[y][x]) {
            return prev[y][x]
          }
          return { color: '#FFFFFF' }
        })
      )
      return newPixels
    })
  }, [width, height])

  const handlePixelClick = (x: number, y: number) => {
    if (currentTool === 'eyedropper') {
      onColorPick(pixels[y][x].color)
      return
    }

    const newColor = currentTool === 'eraser' ? '#FFFFFF' : currentColor
    
    setPixels(prev => {
      const newPixels = [...prev]
      newPixels[y] = [...newPixels[y]]
      newPixels[y][x] = { color: newColor }
      return newPixels
    })
  }

  const handleMouseDown = (x: number, y: number) => {
    setIsDrawing(true)
    handlePixelClick(x, y)
  }

  const handleMouseEnter = (x: number, y: number) => {
    if (isDrawing && currentTool !== 'eyedropper') {
      handlePixelClick(x, y)
    }
  }

  const handleMouseUp = () => {
    setIsDrawing(false)
  }

  const exportToPNG = () => {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        ctx.fillStyle = pixels[y][x].color
        ctx.fillRect(x, y, 1, 1)
      }
    }

    const link = document.createElement('a')
    link.download = 'pixel-art.png'
    link.href = canvas.toDataURL()
    link.click()
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <div className="mb-4">
        <button
          onClick={exportToPNG}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          Экспорт в PNG
        </button>
      </div>
      
      <div 
        ref={canvasRef}
        className="inline-block border-2 border-gray-300"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${width}, ${zoom}px)`,
          gridTemplateRows: `repeat(${height}, ${zoom}px)`,
          gap: '1px',
          backgroundColor: '#ccc'
        }}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {pixels.map((row, y) =>
          row.map((pixel, x) => (
            <div
              key={`${x}-${y}`}
              className="border border-gray-200 cursor-pointer"
              style={{
                backgroundColor: pixel.color,
                width: `${zoom}px`,
                height: `${zoom}px`
              }}
              onMouseDown={() => handleMouseDown(x, y)}
              onMouseEnter={() => handleMouseEnter(x, y)}
            />
          ))
        )}
      </div>
    </div>
  )
}

export default PixelCanvas
