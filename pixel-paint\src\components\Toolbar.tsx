interface ToolbarProps {
  currentTool: 'pencil' | 'eyedropper' | 'eraser'
  onToolChange: (tool: 'pencil' | 'eyedropper' | 'eraser') => void
  canvasSize: { width: number; height: number }
  onCanvasSizeChange: (size: { width: number; height: number }) => void
  zoom: number
  onZoomChange: (zoom: number) => void
}

const Toolbar = ({
  currentTool,
  onToolChange,
  canvasSize,
  onCanvasSizeChange,
  zoom,
  onZoomChange
}: ToolbarProps) => {
  const tools = [
    { id: 'pencil' as const, name: 'Карандаш', icon: '✏️' },
    { id: 'eyedropper' as const, name: 'Пипетка', icon: '💧' },
    { id: 'eraser' as const, name: '<PERSON>а<PERSON>ти<PERSON>', icon: '🧽' }
  ]

  const handleSizeChange = (dimension: 'width' | 'height', value: string) => {
    const numValue = Math.max(1, Math.min(100, parseInt(value) || 1))
    onCanvasSizeChange({
      ...canvasSize,
      [dimension]: numValue
    })
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold mb-4 text-gray-800">Инструменты</h3>
      
      {/* Инструменты */}
      <div className="space-y-2 mb-6">
        {tools.map(tool => (
          <button
            key={tool.id}
            onClick={() => onToolChange(tool.id)}
            className={`w-full p-3 rounded-lg border-2 transition-colors ${
              currentTool === tool.id
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300 text-gray-700'
            }`}
          >
            <span className="text-xl mr-2">{tool.icon}</span>
            {tool.name}
          </button>
        ))}
      </div>

      {/* Размер холста */}
      <div className="mb-6">
        <h4 className="text-md font-medium mb-2 text-gray-700">Размер холста</h4>
        <div className="space-y-2">
          <div>
            <label className="block text-sm text-gray-600 mb-1">Ширина</label>
            <input
              type="number"
              min="1"
              max="100"
              value={canvasSize.width}
              onChange={(e) => handleSizeChange('width', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Высота</label>
            <input
              type="number"
              min="1"
              max="100"
              value={canvasSize.height}
              onChange={(e) => handleSizeChange('height', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Масштаб */}
      <div>
        <h4 className="text-md font-medium mb-2 text-gray-700">
          Масштаб: {zoom}px
        </h4>
        <input
          type="range"
          min="5"
          max="50"
          value={zoom}
          onChange={(e) => onZoomChange(parseInt(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>5px</span>
          <span>50px</span>
        </div>
      </div>
    </div>
  )
}

export default Toolbar
